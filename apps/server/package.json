{"name": "@coozf/server", "private": true, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "engines": {"node": ">=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.js"}, "./trpc": {"types": "./dist/index.d.ts", "default": "./src/router/index.ts"}}, "scripts": {"build": "node esbuild.config.mjs", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "check-types": "tsc --noEmit", "lint": "eslint src --ext .ts --cache", "with-env": "dotenv -e ../../.env.local --", "generate": "pnpm with-env prisma generate", "migrate": "pnpm with-env prisma migrate dev", "reset": "pnpm with-env prisma migrate reset", "push": "pnpm with-env prisma db push", "studio": "pnpm with-env prisma studio", "pm2:dev": "pm2 start ecosystem.config.cjs --env dev", "pm2:prod": "npm run build && pm2 start ecosystem.config.cjs --env prod", "pm2:pre": "pm2 start ecosystem.config.cjs --env pre", "pm2:stop": "pm2 stop coozf-server", "pm2:restart": "pm2 restart coozf-server", "pm2:reload": "pm2 reload coozf-server", "pm2:delete": "pm2 delete coozf-server", "pm2:logs": "pm2 logs coozf-server", "pm2:monit": "pm2 monit"}, "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.2", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-util": "^1.4.10", "@coozf/eslint-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "@coozf/db": "workspace:*", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/http-proxy": "^11.3.0", "@fastify/static": "^8.2.0", "@prisma/client": "^6.11.1", "@trpc/server": "11.4.2", "@types/bcrypt": "^5.0.2", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "fastify": "^5.4.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "mysql2": "^3.14.1", "nanoid": "^5.1.5", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "prisma": "^6.11.1", "zod": "^3.25.67", "zod-openapi": "^4.2.4"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.3", "@vercel/ncc": "^0.38.3", "esbuild": "0.25.5", "trpc-to-openapi": "^2.3.2", "trpc-ui": "^1.0.15", "tsx": "^4.20.3"}}