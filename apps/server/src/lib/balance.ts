import { db } from '@coozf/db'
import {
  ApplicationBalanceRepository,
  TransactionRepository,
  ApiCallRepository
} from '@coozf/db'

// 创建 Repository 实例
const balanceRepo = new ApplicationBalanceRepository(db)
const transactionRepo = new TransactionRepository(db)
const apiCallRepo = new ApiCallRepository(db)

/**
 * 获取应用蚁贝余额
 */
export async function getApplicationBalance(applicationId: string): Promise<string> {
  const balance = await balanceRepo.findByApplicationId(applicationId)

  if (!balance) {
    // 如果应用没有余额记录，创建一个初始余额为0的记录
    const newBalance = await balanceRepo.create({
      balance: 0,
      application: {
        connect: { id: applicationId }
      }
    })
    return newBalance.balance.toString()
  }

  return balance.balance.toString()
}

/**
 * 创建交易记录并更新应用余额
 */
export async function createTransaction(
  applicationId: string,
  type: 'RECHARGE' | 'CONSUME' | 'REFUND',
  amount: number,
  relatedId: string,
  relatedType: string,
  description?: string
): Promise<void> {
  // 使用 Repository 的事务方法
  await transactionRepo.createWithBalanceUpdate({
    applicationId,
    type,
    amount,
    description,
    relatedId,
    relatedType,
  })
}

/**
 * 充值蚁贝到应用
 */
export async function rechargeBalance(
  applicationId: string,
  amount: number,
  relatedId: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, 'RECHARGE', amount, relatedId, 'order', description)
}

/**
 * 从应用消费蚁贝
 */
export async function consumeBalance(
  applicationId: string,
  amount: number,
  relatedId: string,
  relatedType: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, 'CONSUME', amount, relatedId, relatedType, description)
}

/**
 * 退款蚁贝到应用
 */
export async function refundBalance(
  applicationId: string,
  amount: number,
  relatedId: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, 'REFUND', amount, relatedId, 'order', description)
}

/**
 * 获取应用交易记录
 */
export async function getApplicationTransactions(applicationId: string, page: number = 1, pageSize: number = 10) {
  const skip = (page - 1) * pageSize

  const records = await transactionRepo.findByApplicationId(applicationId, {
    skip,
    take: pageSize,
  })

  return records
}

/**
 * 检查应用余额是否足够
 */
export async function checkApplicationBalance(applicationId: string, requiredAmount: number): Promise<boolean> {
  const balance = await getApplicationBalance(applicationId)
  return parseFloat(balance) >= requiredAmount
}

/**
 * 记录API调用并扣费
 */
export async function recordApiCall(
  applicationId: string,
  endpoint: string,
  method: string,
  costType: 'ACCOUNT_QUOTA' | 'TRAFFIC',
  costAmount: number
): Promise<void> {
  // 使用 Repository 的事务方法
  await apiCallRepo.recordApiCallWithCost({
    applicationId,
    endpoint,
    method,
    costType,
    costAmount,
  })
}

/**
 * 获取API调用记录
 */
export async function getApiCalls(
  applicationId: string,
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    endpoint?: string
    costType?: 'ACCOUNT_QUOTA' | 'TRAFFIC'
    startDate?: string
    endDate?: string
  }
) {
  const skip = (page - 1) * pageSize

  const records = await apiCallRepo.findByApplicationId(applicationId, {
    skip,
    take: pageSize,
    endpoint: filters?.endpoint,
    costType: filters?.costType,
  })

  // 如果有日期过滤，需要在内存中过滤（或者扩展 Repository 方法）
  let filteredRecords = records
  if (filters?.startDate || filters?.endDate) {
    filteredRecords = records.filter(record => {
      const recordDate = new Date(record.createdAt)
      if (filters.startDate && recordDate < new Date(filters.startDate)) return false
      if (filters.endDate && recordDate > new Date(filters.endDate)) return false
      return true
    })
  }

  return filteredRecords
}

/**
 * 计算购买账号额度所需蚁贝
 */
export function calculateAccountQuotaCost(quotaCount: number): number {
  return quotaCount * 40 // 1个账号额度 = 40蚁贝
}

/**
 * 计算购买流量所需蚁贝
 */
export function calculateTrafficCost(trafficGB: number): number {
  return trafficGB * 1 // 1GB流量 = 1蚁贝
}

/**
 * 自动扣费：流量
 */
export async function autoDeductTraffic(
  applicationId: string,
  trafficGB: number,
  endpoint: string,
  method: string = 'GET'
): Promise<void> {
  const cost = calculateTrafficCost(trafficGB)
  await recordApiCall(applicationId, endpoint, method, 'TRAFFIC', cost)
}
