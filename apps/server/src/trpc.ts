import { initTRPC } from '@trpc/server'
import { ZodError } from 'zod'
import type { OpenApiMeta } from 'trpc-to-openapi'
import superjson from 'superjson'
import type { Context } from './context'
import { isDev } from './env'
import { ResponseWrapper, ApiError } from './lib/response'
import { getHTTPStatusCodeFromError } from '@trpc/server/unstable-core-do-not-import'

export const t = initTRPC
  .meta<OpenApiMeta>()
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error, ctx } = opts

      if (ctx?.isPublicApi) {
        if (error.cause instanceof ZodError) {
          const validationErrors = error.cause.flatten().fieldErrors
          const firstError = Object.values(validationErrors)[0]?.[0] || 'Validation failed'
          return ResponseWrapper.error(40001, firstError)
        }

        if (error instanceof ApiError || error.cause instanceof ApiError) {
          const apiError = (error instanceof ApiError ? error : error.cause) as ApiError
          return ResponseWrapper.error(apiError.code, apiError.message)
        }

        const httpStatus = getHTTPStatusCodeFromError(error)
        return ResponseWrapper.error(httpStatus * 100, error.message)
      }

      if (error.code === 'INTERNAL_SERVER_ERROR' && !isDev) {
        return { ...shape, message: 'Internal server error' }
      }
      return {
        ...shape,
        data: {
          ...shape.data,
          zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
        },
      }
    },
  })

export const publicProcedure = t.procedure
export const router = t.router
