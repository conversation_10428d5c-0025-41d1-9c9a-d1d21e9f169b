import { z } from 'zod'

// 平台枚举
export const PlatformEnum = z.enum(['xia<PERSON><PERSON><PERSON>', 'douyin', 'kuaishou', 'weibo'])

// 授权账号相关的 Zod Schema
export const CreateAuthAccountSchema = z.object({
  platform: PlatformEnum,
  platformUserId: z.string().min(1, '平台用户ID不能为空'),
  userInfo: z.record(z.unknown()).optional(), // JSON 对象
  state: z.string().optional(),
  scope: z.string().optional(),
})

export const UpdateAuthAccountSchema = z.object({
  platform: PlatformEnum.optional(),
  platformUserId: z.string().min(1, '平台用户ID不能为空').optional(),
  userInfo: z.record(z.unknown()).optional(),
  state: z.string().optional(),
  scope: z.string().optional(),
})

export const SelectAuthAccountSchema = z.object({
  id: z.string(),
  appId: z.string(),
  platform: PlatformEnum,
  platformUserId: z.string(),
  userInfo: z.record(z.unknown()).nullable(),
  state: z.string().nullable(),
  scope: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 授权账号列表查询参数
export const AuthAccountListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  appId: z.string().optional(),
  platform: PlatformEnum.optional(),
  platformUserId: z.string().optional(),
})

// 类型推导
export type Platform = z.infer<typeof PlatformEnum>
export type CreateAuthAccountInput = z.infer<typeof CreateAuthAccountSchema>
export type UpdateAuthAccountInput = z.infer<typeof UpdateAuthAccountSchema>
export type SelectAuthAccountOutput = z.infer<typeof SelectAuthAccountSchema>
export type AuthAccountListParams = z.infer<typeof AuthAccountListSchema>
