import { z } from 'zod'

// 用户相关的 Zod Schema
export const CreateUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
  avatar: z.string().url('请输入有效的头像地址').optional(),
})

export const UpdateUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
  avatar: z.string().url('请输入有效的头像地址').optional(),
  phoneVerified: z.boolean().optional(),
  emailVerified: z.boolean().optional(),
})

export const LoginSchema = z
  .object({
    email: z.string().email('请输入有效的邮箱地址').optional(),
    phone: z
      .string()
      .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
      .optional(),
    password: z.string().min(6, '密码至少6位'),
  })
  .refine((data) => data.email ?? data.phone, {
    message: '请输入邮箱或手机号',
  })

export const SelectUserSchema = z.object({
  id: z.string(),
  email: z.string().nullable(),
  phone: z.string().nullable(),
  name: z.string().nullable(),
  phoneVerified: z.boolean(),
  emailVerified: z.boolean(),
  avatar: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 类型推导
export type CreateUserInput = z.infer<typeof CreateUserSchema>
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>
export type LoginInput = z.infer<typeof LoginSchema>
export type SelectUserOutput = z.infer<typeof SelectUserSchema>
