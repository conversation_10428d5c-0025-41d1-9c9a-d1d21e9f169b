import type { Application } from '@coozf/db'
import { TRPCError } from '@trpc/server'
import { LRUCache } from 'lru-cache'
import z from 'zod'
import { protectedProcedure } from '.'

const appPermissionCache = new LRUCache<string, Application>({
  max: 5000,
  ttl: 5 * 60 * 1000, // 5分钟权限缓存
})

export const applicationProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().cuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input
    const cacheKey = `${ctx.user.id}:${applicationId}`

    // 检查权限缓存
    if (!appPermissionCache.has(cacheKey)) {
      // 缓存未命中，查询数据库验证权限
      const existingApp = await ctx.repo.appRepo.findByUserIdAndId(ctx.user.id, applicationId)

      if (!existingApp) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '应用不存在或无权限访问',
        })
      }

      // 缓存权限验证结果
      appPermissionCache.set(cacheKey, existingApp)
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        application: appPermissionCache.get(cacheKey)!,
      },
    })
  })

export const applicationWithBalanceProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().cuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input

    // 验证权限并获取应用数据
    const application = await ctx.repo.appRepo.findByUserIdAndIdWithInclude(ctx.user.id, applicationId, {
      applicationBalance: true,
    })

    if (!application) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    const { applicationBalance, ...app } = application

    const applicationWithBalance = {
      ...app,
      balance: applicationBalance?.balance.toNumber() || 0,
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        applicationWithBalance,
      },
    })
  })
