import { db } from '@coozf/db'
import type { Application } from '@coozf/db'
import { ApplicationRepository } from '@coozf/db'
import { sendWebhookNotification } from '@/lib/oauth2'
import { getSessionToken } from '@/lib/session-token'
import type { FastifyInstance } from 'fastify'
import { ApiError } from '@/lib/response'

// 创建 Repository 实例
const appRepo = new ApplicationRepository(db)

declare module 'fastify' {
  interface FastifyRequest {
    application: Application
  }
}

export const crawlerWebhookRoutes = async (app: FastifyInstance) => {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const sessionToken = await getSessionToken(authHeader ?? '')
    // 获取应用信息
    const existingApp = await appRepo.findByUserIdAndId(sessionToken.userId, sessionToken.applicationId)
    if (!existingApp) {
      throw new ApiError(40100, 'Unauthorized')
    }
    request.application = existingApp
  })
  app.post('/', async (request, reply) => {
    const { application } = request
    const { webhookUrl, webhookSecret } = application
    if (!webhookUrl) {
      return reply.status(400).send({
        success: false,
        message: 'Webhook URL not found',
      })
    } else {
      await sendWebhookNotification(webhookUrl, webhookSecret, 'web', request.body)
    }
    return reply.send({
      statusCode: 0,
      message: 'Webhook received',
    })
  })
}
