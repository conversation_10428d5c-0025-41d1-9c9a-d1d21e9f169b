import './env'
import type { FastifyTRPCPluginOptions, CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'
import Fastify from 'fastify'
import fastifyCookie from '@fastify/cookie'
import { openApiDocument } from './openapi'
import createContext from './context'

const createPublicContext = async (opts: CreateFastifyContextOptions) => {
  const context = await createContext(opts)
  return { ...context, isPublicApi: true }
}

import type { AppRouter } from './router'
import { appRouter, openApiRouter } from './router'
import { fastifyTRPCOpenApiPlugin } from 'trpc-to-openapi'
import { config, isDev, isModeProd } from './env'
import routes from './fastify-router'
import fastifyStatic from '@fastify/static'
import path from 'path'

// export const mergeRouters = t.mergeRouters

import { ZodError } from 'zod'
import { ResponseWrapper, ApiError } from './lib/response'
import { TRPCError } from '@trpc/server'
import { getHTTPStatusCodeFromError } from '@trpc/server/http'

const app = Fastify({
  logger: {
    level: !isModeProd ? 'info' : 'warn',
    serializers: {
      req: (req) => ({
        method: req.method,
        url: req.url.split('?')[0], // 移除查询参数
      }),
      res: (res) => ({
        statusCode: res.statusCode,
      }),
    },
    transport: !isModeProd
      ? {
          target: 'pino-pretty',
          options: {
            translateTime: 'HH:MM:ss',
            ignore: 'pid,hostname,reqId,responseTime',
            colorize: true,
            levelFirst: false,
            singleLine: true,
            messageFormat: '{msg} {req.method} {req.url} {res.statusCode}',
          },
        }
      : undefined,
  },
})

async function main() {
  await app.register(fastifyCookie)

  await app.register(fastifyStatic, {
    root: path.join(__dirname, '../public'),
    prefix: '/',
  })

  // SPA路由支持
  app.setNotFoundHandler(async (request, reply) => {
    if (request.url.startsWith('/api/')) {
      return reply.status(404).send(ResponseWrapper.error(40400, 'API route not found'))
    }
    return reply.sendFile('index.html')
  })

  await app.register(fastifyTRPCPlugin, {
    prefix: '/api/trpc',
    useWSS: false,
    trpcOptions: {
      router: appRouter,
      createContext,
      onError({ path, error }) {
        // report to error monitoring
        console.error(`Error in tRPC handler on path '${path}':`, error)
      },
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
  })

  await app.register(fastifyTRPCOpenApiPlugin, {
    basePath: '/api/open',
    router: openApiRouter,
    createContext: createPublicContext,
  })

  app.get('/openapi.json', () => openApiDocument)

  // 注册路由
  app.register(routes, { prefix: '/api' })

  app.get('/panel', async (_, res) => {
    if (!isDev) {
      return res.status(404).send('Not Found')
    }

    // Dynamically import renderTrpcPanel only in dev
    const { renderTrpcPanel } = await import('trpc-ui')

    return res
      .status(200)
      .header('Content-Type', 'text/html')
      .send(
        renderTrpcPanel(appRouter, {
          url: '/api/trpc',
          transformer: 'superjson',
          meta: {
            title: 'trpc-server',
            description: 'trpc-server',
          },
        }),
      )
  })

  app.setErrorHandler((error, _, reply) => {
    if (error instanceof ZodError) {
      const validationErrors = error.flatten().fieldErrors
      const firstError = Object.values(validationErrors)[0]?.[0] ?? 'Validation failed'
      reply.status(400).send(ResponseWrapper.error(40001, firstError))
    } else if (error instanceof ApiError) {
      reply.status(Math.floor(error.code / 100)).send(ResponseWrapper.error(error.code, error.message))
    } else if (error instanceof TRPCError) {
      app.log.error(error)
      const httpStatus = getHTTPStatusCodeFromError(error)
      reply.status(httpStatus).send(ResponseWrapper.error(httpStatus * 100, error.message))
    } else {
      app.log.error(error)
      reply.status(500).send(ResponseWrapper.error(50000, 'Internal Server Error'))
    }
  })

  const port = Number(config.PORT) || 2022
  await app.listen({
    port,
    host: '0.0.0.0',
  })

  app.log.info(`🚀 服务器启动成功！`)
  app.log.info(`📡 API 地址: http://localhost:${port}/trpc`)
  app.log.info(`🎛️  控制面板: http://localhost:${port}/panel`)
  app.log.info(`🌍 环境: ${config.NODE_ENV}`)
}

main().catch((err) => {
  app.log.error(err, '❌ 服务器启动失败')
  console.error(err)
  process.exit(1)
})
