# ---- Builder Stage ----
# 此阶段负责安装所有依赖并构建项目
FROM node:20-alpine AS builder

# 设置构建参数
ARG PACKAGE_SCRIPT=prod

# 设置镜像源并安装 pnpm
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制 monorepo 配置文件和所有 package.json
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
RUN mkdir -p apps/client apps/server packages/ui packages/db
COPY apps/client/package.json ./apps/client/
COPY apps/server/package.json ./apps/server/
COPY packages/ui/package.json ./packages/ui/
COPY packages/db/package.json ./packages/db/

# 使用 pnpm 安装所有依赖 (包括 devDependencies)
RUN pnpm install --frozen-lockfile

# 复制所有源代码
COPY . .

# 构建前端项目
RUN pnpm run build:copy-frontend

# 构建后端项目
RUN pnpm run build:server


# ---- Runner Stage ----
# 此阶段只包含运行应用所需的最小文件集
FROM node:20-alpine

# 设置默认环境，此值可以在 docker run 时通过 -e PACKAGE_SCRIPT=<env> 覆盖
ARG PACKAGE_SCRIPT=prod
ENV PACKAGE_SCRIPT=${PACKAGE_SCRIPT}


# 设置镜像源并安装 pnpm 和 pm2
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm pm2

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制 monorepo 配置文件和所有 package.json
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
RUN mkdir -p apps/client apps/server
COPY --from=builder /app/apps/client/package.json ./apps/client/
COPY --from=builder /app/apps/server/package.json ./apps/server/

# 只安装生产依赖，并忽略所有脚本
RUN pnpm install --prod --frozen-lockfile --ignore-scripts

# 从 builder 阶段复制构建产物和运行时配置文件
COPY --from=builder /app/apps/server/dist ./apps/server/dist
COPY --from=builder /app/apps/server/public ./apps/server/public
COPY --from=builder /app/apps/server/ecosystem.config.cjs ./apps/server/ecosystem.config.cjs

# 【推荐修改 2】同时复制 prisma schema 文件，某些情况下运行时可能需要
COPY --from=builder /app/apps/server/prisma ./apps/server/prisma

RUN pnpm run generate

# 复制并标准化所有环境的 .env 文件
# PM2 会根据 --env <name> 参数加载对应的 .env.<name> 文件
COPY --from=builder /app/.env.dev ./.env.dev
COPY --from=builder /app/.env.pre ./.env.pre
COPY --from=builder /app/.env.prod ./.env.prod
# 暴露端口
EXPOSE 3000

# 切换到 server 目录并启动应用
# 使用 sh -c 确保 PACKAGE_SCRIPT 环境变量能被正确解析
WORKDIR /app/apps/server
CMD ["sh", "-c", "pm2-runtime start ecosystem.config.cjs --env \"$PACKAGE_SCRIPT\""]