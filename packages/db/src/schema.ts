
// 导出 Zod schemas（避免与 Prisma 枚举冲突）
export {
  // 用户相关
  CreateUserSchema,
  UpdateUserSchema,
  LoginSchema,
  SelectUserSchema,
  type CreateUserInput,
  type UpdateUserInput,
  type LoginInput,
  type SelectUserOutput,

  // 应用相关
  CreateApplicationSchema,
  UpdateApplicationSchema,
  SelectApplicationSchema,
  ApplicationListSchema,
  type CreateApplicationInput,
  type UpdateApplicationInput,
  type SelectApplicationOutput,
  type ApplicationListParams,

  // 订单相关
  CreateOrderSchema,
  UpdateOrderSchema,
  SelectOrderSchema,
  OrderListSchema,
  AdminRechargeSchema,
  type CreateOrderInput,
  type UpdateOrderInput,
  type SelectOrderOutput,
  type OrderListParams,
  type AdminRechargeParams,

  // 余额和交易相关
  CreateApplicationBalanceSchema,
  UpdateApplicationBalanceSchema,
  CreateTransactionSchema,
  SelectTransactionSchema,
  CreateApiCallSchema,
  SelectApiCallSchema,
  TransactionListSchema,
  ApiCallListSchema,
  type CreateApplicationBalanceInput,
  type UpdateApplicationBalanceInput,
  type CreateTransactionInput,
  type SelectTransactionOutput,
  type CreateApiCallInput,
  type SelectApiCallOutput,
  type TransactionListParams,
  type ApiCallListParams,

  // 授权账号相关
  CreateAuthAccountSchema,
  UpdateAuthAccountSchema,
  SelectAuthAccountSchema,
  AuthAccountListSchema,
  type Platform,
  type CreateAuthAccountInput,
  type UpdateAuthAccountInput,
  type SelectAuthAccountOutput,
  type AuthAccountListParams,
} from './schemas'
