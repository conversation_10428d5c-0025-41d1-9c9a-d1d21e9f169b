import type { Prisma, PrismaClient, Application } from '@prisma/client'
import { BaseRepository } from './base.repository'

export type CreateApplicationInput = Prisma.ApplicationCreateInput
export type UpdateApplicationInput = Prisma.ApplicationUpdateInput

/**
 * 应用 Repository
 */
export class ApplicationRepository extends BaseRepository<Application, CreateApplicationInput, UpdateApplicationInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  /**
   * 创建应用
   */
  async create(data: CreateApplicationInput): Promise<Application> {
    return this.db.application.create({
      data,
    })
  }

  /**
   * 根据 ID 查找应用
   */
  async findById(id: string): Promise<Application | null> {
    return this.db.application.findUnique({
      where: { id },
    })
  }

  /**
   * 查找多个应用
   */
  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ApplicationWhereInput
    orderBy?: Prisma.ApplicationOrderByWithRelationInput
    include?: Prisma.ApplicationInclude
  }): Promise<Application[]> {
    return this.db.application.findMany(params)
  }

  /**
   * 更新应用
   */
  async update(id: string, data: UpdateApplicationInput): Promise<Application> {
    return this.db.application.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除应用
   */
  async delete(id: string): Promise<Application> {
    return this.db.application.delete({
      where: { id },
    })
  }

  /**
   * 统计应用数量
   */
  async count(where?: Prisma.ApplicationWhereInput): Promise<number> {
    return this.db.application.count({ where })
  }

  /**
   * 根据用户 ID 查找应用
   */
  async findByUserId(userId: string): Promise<Application[]> {
    return this.db.application.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    })
  }

  /**
   * 根据用户 ID 和 应用 ID 查找应用
   */
  async findByUserIdAndId(userId: string, id: string) {
    return this.db.application.findUnique({
      where: {
        userId,
        id,
      }
    })
  }

  async findByUserIdAndIdWithInclude(userId: string, id: string, include: Prisma.ApplicationInclude) {
    return this.db.application.findUnique({
      where: {
        userId,
        id,
      },
      include,
    })
  }

  /**
   * 获取应用及其相关数据
   */
  async findWithDetails(id: string): Promise<Application | null> {
    return this.db.application.findUnique({
      where: { id },
      include: {
        user: true,
        authAccounts: true,
        applicationBalance: true,
        transactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        apiCalls: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        orders: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    })
  }

  /**
   * 更新应用状态
   */
  async updateStatus(id: string, status: 'ACTIVE' | 'SUSPENDED' | 'DELETED'): Promise<Application> {
    return this.db.application.update({
      where: { id },
      data: { status },
    })
  }

  /**
   * 获取用户的活跃应用
   */
  async findActiveByUserId(userId: string): Promise<Application[]> {
    return this.db.application.findMany({
      where: {
        userId,
        status: 'ACTIVE',
      },
      orderBy: { createdAt: 'desc' },
    })
  }
}
