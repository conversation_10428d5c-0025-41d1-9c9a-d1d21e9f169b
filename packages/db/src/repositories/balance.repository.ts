import type { <PERSON><PERSON>alance, Transaction, ApiCall, Prisma, PrismaClient } from '@prisma/client'
import { BaseRepository } from './base.repository'

export type CreateApplicationBalanceInput = Prisma.ApplicationBalanceCreateInput
export type UpdateApplicationBalanceInput = Prisma.ApplicationBalanceUpdateInput
export type CreateTransactionInput = Prisma.TransactionCreateInput
export type CreateApiCallInput = Prisma.ApiCallCreateInput

/**
 * 应用余额 Repository
 */
export class ApplicationBalanceRepository extends BaseRepository<ApplicationBalance, CreateApplicationBalanceInput, UpdateApplicationBalanceInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  async create(data: CreateApplicationBalanceInput): Promise<ApplicationBalance> {
    return this.db.applicationBalance.create({ data })
  }

  async findById(id: string): Promise<ApplicationBalance | null> {
    return this.db.applicationBalance.findUnique({ where: { id } })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ApplicationBalanceWhereInput
    orderBy?: Prisma.ApplicationBalanceOrderByWithRelationInput
    include?: Prisma.ApplicationBalanceInclude
  }): Promise<ApplicationBalance[]> {
    return this.db.applicationBalance.findMany(params)
  }

  async update(id: string, data: UpdateApplicationBalanceInput): Promise<ApplicationBalance> {
    return this.db.applicationBalance.update({ where: { id }, data })
  }

  async delete(id: string): Promise<ApplicationBalance> {
    return this.db.applicationBalance.delete({ where: { id } })
  }

  async count(where?: Prisma.ApplicationBalanceWhereInput): Promise<number> {
    return this.db.applicationBalance.count({ where })
  }

  /**
   * 根据应用 ID 查找余额
   */
  async findByApplicationId(applicationId: string): Promise<ApplicationBalance | null> {
    return this.db.applicationBalance.findUnique({
      where: { applicationId },
    })
  }

  /**
   * 更新应用余额
   */
  async updateBalance(applicationId: string, balance: number): Promise<ApplicationBalance> {
    return this.db.applicationBalance.update({
      where: { applicationId },
      data: { balance },
    })
  }

  /**
   * 增加余额
   */
  async increaseBalance(applicationId: string, amount: number): Promise<ApplicationBalance> {
    return this.db.applicationBalance.update({
      where: { applicationId },
      data: {
        balance: {
          increment: amount,
        },
      },
    })
  }

  /**
   * 减少余额
   */
  async decreaseBalance(applicationId: string, amount: number): Promise<ApplicationBalance> {
    return this.db.applicationBalance.update({
      where: { applicationId },
      data: {
        balance: {
          decrement: amount,
        },
      },
    })
  }
}

/**
 * 交易记录 Repository
 */
export class TransactionRepository extends BaseRepository<Transaction, CreateTransactionInput, Prisma.TransactionUpdateInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  async create(data: CreateTransactionInput): Promise<Transaction> {
    return this.db.transaction.create({ data })
  }

  async findById(id: string): Promise<Transaction | null> {
    return this.db.transaction.findUnique({ where: { id } })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.TransactionWhereInput
    orderBy?: Prisma.TransactionOrderByWithRelationInput
    include?: Prisma.TransactionInclude
  }): Promise<Transaction[]> {
    return this.db.transaction.findMany(params)
  }

  async update(id: string, data: Prisma.TransactionUpdateInput): Promise<Transaction> {
    return this.db.transaction.update({ where: { id }, data })
  }

  async delete(id: string): Promise<Transaction> {
    return this.db.transaction.delete({ where: { id } })
  }

  async count(where?: Prisma.TransactionWhereInput): Promise<number> {
    return this.db.transaction.count({ where })
  }

  /**
   * 根据应用 ID 查找交易记录
   */
  async findByApplicationId(applicationId: string, params?: {
    skip?: number
    take?: number
    type?: 'RECHARGE' | 'CONSUME' | 'REFUND'
  }): Promise<Transaction[]> {
    const where: Prisma.TransactionWhereInput = { applicationId }
    
    if (params?.type) {
      where.type = params.type
    }

    return this.db.transaction.findMany({
      where,
      skip: params?.skip,
      take: params?.take,
      orderBy: { createdAt: 'desc' },
      include: {
        application: true,
      },
    })
  }

  /**
   * 创建交易记录（带余额更新）
   */
  async createWithBalanceUpdate(data: {
    applicationId: string
    type: 'RECHARGE' | 'CONSUME' | 'REFUND'
    amount: number
    description?: string
    relatedId: string
    relatedType: string
  }): Promise<Transaction> {
    return this.db.$transaction(async (tx: { applicationBalance: { findUnique: (arg0: { where: { applicationId: string } }) => any; update: (arg0: { where: { applicationId: string }; data: { balance: number } }) => any }; transaction: { create: (arg0: { data: { applicationId: string; type: "RECHARGE" | "CONSUME" | "REFUND"; amount: number; beforeBalance: number; afterBalance: number; description: string | undefined; relatedId: string; relatedType: string } }) => any } }) => {
      // 获取当前余额
      const balance = await tx.applicationBalance.findUnique({
        where: { applicationId: data.applicationId },
      })

      if (!balance) {
        throw new Error('应用余额记录不存在')
      }

      const beforeBalance = Number(balance.balance)
      let afterBalance: number

      // 计算新余额
      if (data.type === 'RECHARGE' || data.type === 'REFUND') {
        afterBalance = beforeBalance + data.amount
      } else {
        afterBalance = beforeBalance - data.amount
        if (afterBalance < 0) {
          throw new Error('余额不足')
        }
      }

      // 更新余额
      await tx.applicationBalance.update({
        where: { applicationId: data.applicationId },
        data: { balance: afterBalance },
      })

      // 创建交易记录
      return tx.transaction.create({
        data: {
          applicationId: data.applicationId,
          type: data.type,
          amount: data.amount,
          beforeBalance,
          afterBalance,
          description: data.description,
          relatedId: data.relatedId,
          relatedType: data.relatedType,
        },
      })
    })
  }
}

/**
 * API 调用记录 Repository
 */
export class ApiCallRepository extends BaseRepository<ApiCall, CreateApiCallInput, Prisma.ApiCallUpdateInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  async create(data: CreateApiCallInput): Promise<ApiCall> {
    return this.db.apiCall.create({ data })
  }

  async findById(id: string): Promise<ApiCall | null> {
    return this.db.apiCall.findUnique({ where: { id } })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ApiCallWhereInput
    orderBy?: Prisma.ApiCallOrderByWithRelationInput
    include?: Prisma.ApiCallInclude
  }): Promise<ApiCall[]> {
    return this.db.apiCall.findMany(params)
  }

  async update(id: string, data: Prisma.ApiCallUpdateInput): Promise<ApiCall> {
    return this.db.apiCall.update({ where: { id }, data })
  }

  async delete(id: string): Promise<ApiCall> {
    return this.db.apiCall.delete({ where: { id } })
  }

  async count(where?: Prisma.ApiCallWhereInput): Promise<number> {
    return this.db.apiCall.count({ where })
  }

  /**
   * 根据应用 ID 查找 API 调用记录
   */
  async findByApplicationId(applicationId: string, params?: {
    skip?: number
    take?: number
    endpoint?: string
    costType?: 'ACCOUNT_QUOTA' | 'TRAFFIC'
  }): Promise<ApiCall[]> {
    const where: Prisma.ApiCallWhereInput = { applicationId }
    
    if (params?.endpoint) {
      where.endpoint = { contains: params.endpoint }
    }
    
    if (params?.costType) {
      where.costType = params.costType
    }

    return this.db.apiCall.findMany({
      where,
      skip: params?.skip,
      take: params?.take,
      orderBy: { createdAt: 'desc' },
      include: {
        application: true,
      },
    })
  }

  /**
   * 记录 API 调用并扣费
   */
  async recordApiCallWithCost(data: {
    applicationId: string
    endpoint: string
    method: string
    costType: 'ACCOUNT_QUOTA' | 'TRAFFIC'
    costAmount: number
    statusCode?: number
  }): Promise<ApiCall> {
    return this.db.$transaction(async (tx: { applicationBalance: { findUnique: (arg0: { where: { applicationId: string } }) => any; update: (arg0: { where: { applicationId: string }; data: { balance: number } }) => any }; apiCall: { create: (arg0: { data: { applicationId: string; endpoint: string; method: string; costType: "ACCOUNT_QUOTA" | "TRAFFIC"; costAmount: number; statusCode: number | undefined } }) => any }; transaction: { create: (arg0: { data: { applicationId: string; type: "CONSUME"; amount: number; beforeBalance: number; afterBalance: number; description: string | undefined; relatedId: string; relatedType: string } }) => any } }) => {
      // 检查余额
      const balance = await tx.applicationBalance.findUnique({
        where: { applicationId: data.applicationId },
      })

      if (!balance) {
        throw new Error('应用余额记录不存在')
      }

      const currentBalance = Number(balance.balance)
      if (currentBalance < data.costAmount) {
        throw new Error('余额不足')
      }

      // 创建 API 调用记录
      const apiCall = await tx.apiCall.create({
        data: {
          applicationId: data.applicationId,
          endpoint: data.endpoint,
          method: data.method,
          costType: data.costType,
          costAmount: data.costAmount,
          statusCode: data.statusCode,
        },
      })

      // 扣费
      await tx.applicationBalance.update({
        where: { applicationId: data.applicationId },
        data: {
          balance: currentBalance - data.costAmount,
        },
      })

      // 创建交易记录
      await tx.transaction.create({
        data: {
          applicationId: data.applicationId,
          type: 'CONSUME',
          amount: data.costAmount,
          beforeBalance: currentBalance,
          afterBalance: currentBalance - data.costAmount,
          description: `API调用扣费: ${data.endpoint}`,
          relatedId: apiCall.id,
          relatedType: 'api_call',
        },
      })

      return apiCall
    })
  }
}
