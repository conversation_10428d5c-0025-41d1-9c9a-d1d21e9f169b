import type { AuthAccount, Prisma, PrismaClient } from '@prisma/client'
import { BaseRepository } from './base.repository'

export type CreateAuthAccountInput = Prisma.AuthAccountCreateInput
export type UpdateAuthAccountInput = Prisma.AuthAccountUpdateInput

/**
 * 授权账号 Repository
 */
export class AuthAccountRepository extends BaseRepository<AuthAccount, CreateAuthAccountInput, UpdateAuthAccountInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  /**
   * 创建授权账号
   */
  async create(data: CreateAuthAccountInput): Promise<AuthAccount> {
    return this.db.authAccount.create({
      data,
    })
  }

  /**
   * 根据 ID 查找授权账号
   */
  async findById(id: string): Promise<AuthAccount | null> {
    return this.db.authAccount.findUnique({
      where: { id },
    })
  }

  /**
   * 查找多个授权账号
   */
  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.AuthAccountWhereInput
    orderBy?: Prisma.AuthAccountOrderByWithRelationInput
    include?: Prisma.AuthAccountInclude
  }): Promise<AuthAccount[]> {
    return this.db.authAccount.findMany(params)
  }

  /**
   * 更新授权账号
   */
  async update(id: string, data: UpdateAuthAccountInput): Promise<AuthAccount> {
    return this.db.authAccount.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除授权账号
   */
  async delete(id: string): Promise<AuthAccount> {
    return this.db.authAccount.delete({
      where: { id },
    })
  }

  /**
   * 统计授权账号数量
   */
  async count(where?: Prisma.AuthAccountWhereInput): Promise<number> {
    return this.db.authAccount.count({ where })
  }

  /**
   * 根据应用 ID 查找授权账号
   */
  async findByAppId(appId: string): Promise<AuthAccount[]> {
    return this.db.authAccount.findMany({
      where: { appId },
      orderBy: { createdAt: 'desc' },
      include: {
        application: true,
      },
    })
  }

  /**
   * 根据平台查找授权账号
   */
  async findByPlatform(platform: string, params?: {
    skip?: number
    take?: number
    appId?: string
  }): Promise<AuthAccount[]> {
    const where: Prisma.AuthAccountWhereInput = { platform }
    
    if (params?.appId) {
      where.appId = params.appId
    }

    return this.db.authAccount.findMany({
      where,
      skip: params?.skip,
      take: params?.take,
      orderBy: { createdAt: 'desc' },
      include: {
        application: true,
      },
    })
  }

  /**
   * 根据平台用户 ID 查找授权账号
   */
  async findByPlatformUserId(platformUserId: string, platform?: string) {
    const where: Prisma.AuthAccountWhereInput = { platformUserId }

    if (platform) {
      where.platform = platform
    }

    return this.db.authAccount.findFirst({
      where,
      include: {
        application: true,
      },
    })
  }

  /**
   * 查找特定应用和平台的授权账号
   */
  async findByAppIdAndPlatform(appId: string, platform: string): Promise<AuthAccount[]> {
    return this.db.authAccount.findMany({
      where: {
        appId,
        platform,
      },
      orderBy: { createdAt: 'desc' },
      include: {
        application: true,
      },
    })
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(id: string, userInfo: Prisma.InputJsonValue) {
    return this.db.authAccount.update({
      where: { id },
      data: { userInfo },
    })
  }

  /**
   * 更新授权范围
   */
  async updateScope(id: string, scope: string): Promise<AuthAccount> {
    return this.db.authAccount.update({
      where: { id },
      data: { scope },
    })
  }

  /**
   * 批量删除应用的授权账号
   */
  async deleteByAppId(appId: string): Promise<number> {
    const result = await this.db.authAccount.deleteMany({
      where: { appId },
    })
    return result.count
  }

  /**
   * 获取平台统计信息
   */
  async getPlatformStats(appId?: string): Promise<{
    platform: string
    count: number
  }[]> {
    const where: Prisma.AuthAccountWhereInput = {}
    
    if (appId) {
      where.appId = appId
    }

    const result = await this.db.authAccount.groupBy({
      by: ['platform'],
      where,
      _count: {
        platform: true,
      },
    })

    return result.map((item: { platform: string; _count: { platform: number } }) => ({
      platform: item.platform,
      count: item._count.platform,
    }))
  }

  /**
   * 检查授权账号是否存在
   */
  async existsByPlatformUserId(platformUserId: string, platform: string, appId: string): Promise<boolean> {
    const count = await this.db.authAccount.count({
      where: {
        platformUserId,
        platform,
        appId,
      },
    })
    return count > 0
  }
}
