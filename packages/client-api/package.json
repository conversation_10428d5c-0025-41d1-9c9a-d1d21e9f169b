{"name": "@cooz/client-api", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@coozf/db": "workspace:*", "@trpc/server": "catalog:", "superjson": "2.2.2", "zod": "catalog:"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/tsconfig": "workspace:*"}}