import { z } from 'zod'

/**
 * 验证相关的 Zod Schema
 */

// 手机号验证
export const phoneSchema = z
  .string()
  .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')

// 邮箱验证
export const emailSchema = z
  .string()
  .email('请输入有效的邮箱地址')

// 密码验证
export const passwordSchema = z
  .string()
  .min(6, '密码至少6位')
  .max(50, '密码最多50位')

// 验证码验证
export const codeSchema = z
  .string()
  .regex(/^\d{6}$/, '验证码必须是6位数字')

// 用户名验证
export const nameSchema = z
  .string()
  .min(1, '姓名不能为空')
  .max(50, '姓名最多50位')

// 发送验证码请求验证
export const sendCodeSchema = z.object({
  phone: phoneSchema,
  type: z.enum(['login', 'register', 'reset-password']),
})

// 验证码登录请求验证
export const phoneLoginSchema = z.object({
  phone: phoneSchema,
  code: codeSchema,
})

// 密码登录请求验证
export const passwordLoginSchema = z.object({
  phone: phoneSchema.optional(),
  email: emailSchema.optional(),
  password: passwordSchema,
}).refine(
  (data) => data.phone || data.email,
  {
    message: '请输入手机号或邮箱',
    path: ['phone'],
  }
)

// 注册请求验证
export const registerSchema = z.object({
  phone: phoneSchema.optional(),
  email: emailSchema.optional(),
  password: passwordSchema.optional(),
  name: nameSchema.optional(),
  code: codeSchema.optional(),
}).refine(
  (data) => {
    // 手机号注册必须有验证码
    if (data.phone && !data.code) {
      return false
    }
    // 邮箱注册必须有密码
    if (data.email && !data.password) {
      return false
    }
    // 必须有手机号或邮箱
    return data.phone || data.email
  },
  {
    message: '注册信息不完整',
  }
)

// 重置密码请求验证
export const resetPasswordSchema = z.object({
  phone: phoneSchema,
  code: codeSchema,
  newPassword: passwordSchema,
})

// 更新用户信息验证
export const updateUserSchema = z.object({
  name: nameSchema.optional(),
  email: emailSchema.optional(),
  avatar: z.string().url('请输入有效的头像地址').optional(),
})

/**
 * 验证工具函数
 */

// 验证手机号
export function validatePhone(phone: string): boolean {
  return phoneSchema.safeParse(phone).success
}

// 验证邮箱
export function validateEmail(email: string): boolean {
  return emailSchema.safeParse(email).success
}

// 验证密码强度
export function validatePassword(password: string): boolean {
  return passwordSchema.safeParse(password).success
}

// 验证验证码格式
export function validateCode(code: string): boolean {
  return codeSchema.safeParse(code).success
}

// 获取客户端IP地址
export function getClientIp(request: Request): string {
  // 尝试从各种头部获取真实IP
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip',
  ]

  for (const header of headers) {
    const value = request.headers.get(header)
    if (value) {
      // x-forwarded-for 可能包含多个IP，取第一个
      return value.split(',')[0].trim()
    }
  }

  // 如果都没有，返回默认值
  return '127.0.0.1'
}

// 获取用户代理
export function getUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'Unknown'
}
