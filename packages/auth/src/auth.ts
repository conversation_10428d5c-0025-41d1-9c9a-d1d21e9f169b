import { betterAuth } from 'better-auth'
import { phoneNumber } from 'better-auth/plugins'
import { prismaAdapter } from 'better-auth/adapters/prisma'
import { db } from '@coozf/db'
import { env } from './env'
import { nanoid } from 'nanoid'

/**
 * Better Auth 配置
 * 集成 Prisma 适配器和手机号验证码功能
 */
export const auth = betterAuth({
  // 数据库适配器
  database: prismaAdapter(db, {
    provider: 'mysql',
  }),

  // 基础配置
  appName: 'COOZF Open TRPC',
  baseURL: env.AUTH_BASE_URL,
  secret: env.AUTH_SECRET,

  // 会话配置
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7天
    updateAge: 60 * 60 * 24, // 1天更新一次
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5分钟
    },
  },

  // 用户配置
  user: {
    additionalFields: {
      phone: {
        type: 'string',
        required: false,
        unique: true,
      },
      phoneVerified: {
        type: 'boolean',
        required: false,
        defaultValue: false,
      },
    },
  },

  // 插件配置
  plugins: [
    phoneNumber({
      // 手机号验证码配置
      sendSMS: async (phone: string, code: string) => {
        // 这里集成短信服务
        // 暂时使用控制台输出，后续可以集成阿里云短信、腾讯云短信等
        console.log(`发送验证码到 ${phone}: ${code}`)
        
        // 在开发环境下，可以将验证码存储到数据库或缓存中
        if (env.NODE_ENV === 'development') {
          // 存储验证码到 Verification 表
          await db.verification.create({
            data: {
              identifier: phone,
              value: code,
              expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5分钟过期
            },
          })
        }
        
        return true
      },
      
      // 验证码长度
      otpLength: 6,
      
      // 验证码过期时间（秒）
      expiresIn: 300, // 5分钟
    }),
  ],

  // 邮件配置（可选）
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },

  // 高级配置
  advanced: {
    // 生成用户ID的函数
    generateId: () => {
      // 使用 nanoid 生成ID
      return nanoid()
    },
    
    // 跨域配置
    crossSubDomainCookies: {
      enabled: false,
    },
    
    // 默认重定向URL
    defaultRedirectURL: '/',
  },

  // 错误处理
  logger: {
    level: env.NODE_ENV === 'development' ? 'debug' : 'error',
    disabled: env.NODE_ENV === 'test',
  },
})

// 导出类型
export type Auth = typeof auth
