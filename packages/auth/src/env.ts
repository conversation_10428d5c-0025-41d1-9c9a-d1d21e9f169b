import { z } from 'zod'

/**
 * 认证相关环境变量配置
 */
const envSchema = z.object({
  // 基础环境
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // 认证配置
  AUTH_SECRET: z.string().min(32, '认证密钥至少32位'),
  AUTH_BASE_URL: z.string().url('请提供有效的基础URL'),
  
  // 短信服务配置（可选）
  SMS_ACCESS_KEY_ID: z.string().optional(),
  SMS_ACCESS_KEY_SECRET: z.string().optional(),
  SMS_SIGN_NAME: z.string().optional(),
  SMS_TEMPLATE_CODE: z.string().optional(),
  
  // Redis 配置（用于验证码缓存，可选）
  REDIS_URL: z.string().optional(),
  
  // 验证码配置
  SMS_CODE_EXPIRE_TIME: z.string().default('300'), // 5分钟
  SMS_CODE_LENGTH: z.string().default('6'),
  SMS_RATE_LIMIT: z.string().default('60'), // 60秒内只能发送一次
})

// 验证环境变量
function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    console.error('❌ 环境变量验证失败:')
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`)
      })
    }
    process.exit(1)
  }
}

export const env = validateEnv()

// 导出类型
export type Env = z.infer<typeof envSchema>
