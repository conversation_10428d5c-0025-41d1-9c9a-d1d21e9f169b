import { AuthErrorCode } from './types'

/**
 * 认证相关错误类
 */
export class AuthError extends Error {
  public readonly code: AuthErrorCode
  public readonly statusCode: number
  public readonly details?: any

  constructor(code: AuthErrorCode, message: string, statusCode = 400, details?: any) {
    super(message)
    this.name = 'AuthError'
    this.code = code
    this.statusCode = statusCode
    this.details = details
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      details: this.details,
    }
  }
}

/**
 * 预定义的认证错误
 */
export const AuthErrors = {
  // 用户相关错误
  UserNotFound: () =>
    new AuthError(AuthErrorCode.USER_NOT_FOUND, '用户不存在', 404),
  
  UserAlreadyExists: () =>
    new AuthError(AuthErrorCode.USER_ALREADY_EXISTS, '用户已存在', 409),
  
  InvalidCredentials: () =>
    new AuthError(AuthErrorCode.INVALID_CREDENTIALS, '用户名或密码错误', 401),

  // 验证码相关错误
  InvalidCode: () =>
    new AuthError(AuthErrorCode.INVALID_CODE, '验证码错误', 400),
  
  CodeExpired: () =>
    new AuthError(AuthErrorCode.CODE_EXPIRED, '验证码已过期', 400),
  
  CodeNotFound: () =>
    new AuthError(AuthErrorCode.CODE_NOT_FOUND, '验证码不存在', 400),
  
  RateLimitExceeded: () =>
    new AuthError(AuthErrorCode.RATE_LIMIT_EXCEEDED, '发送验证码过于频繁，请稍后再试', 429),

  // 验证相关错误
  PhoneNotVerified: () =>
    new AuthError(AuthErrorCode.PHONE_NOT_VERIFIED, '手机号未验证', 400),
  
  EmailNotVerified: () =>
    new AuthError(AuthErrorCode.EMAIL_NOT_VERIFIED, '邮箱未验证', 400),

  // 会话相关错误
  SessionExpired: () =>
    new AuthError(AuthErrorCode.SESSION_EXPIRED, '会话已过期，请重新登录', 401),
  
  Unauthorized: () =>
    new AuthError(AuthErrorCode.UNAUTHORIZED, '未授权访问', 401),
  
  Forbidden: () =>
    new AuthError(AuthErrorCode.FORBIDDEN, '权限不足', 403),

  // 系统错误
  InternalError: (message = '系统内部错误') =>
    new AuthError(AuthErrorCode.INTERNAL_ERROR, message, 500),
}

/**
 * 错误处理工具函数
 */
export function isAuthError(error: any): error is AuthError {
  return error instanceof AuthError
}

/**
 * 将错误转换为标准的 API 响应格式
 */
export function formatError(error: any) {
  if (isAuthError(error)) {
    return {
      success: false,
      error: error.code,
      message: error.message,
      details: error.details,
    }
  }

  // 处理其他类型的错误
  if (error instanceof Error) {
    return {
      success: false,
      error: 'UNKNOWN_ERROR',
      message: error.message,
    }
  }

  return {
    success: false,
    error: 'UNKNOWN_ERROR',
    message: '未知错误',
  }
}

/**
 * 错误日志记录
 */
export function logError(error: any, context?: string) {
  const timestamp = new Date().toISOString()
  const contextStr = context ? `[${context}] ` : ''
  
  if (isAuthError(error)) {
    console.error(`${timestamp} ${contextStr}AuthError: ${error.code} - ${error.message}`, error.details)
  } else {
    console.error(`${timestamp} ${contextStr}Error:`, error)
  }
}
