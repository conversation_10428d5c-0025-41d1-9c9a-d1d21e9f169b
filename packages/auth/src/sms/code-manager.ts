import { db } from '@coozf/db'
import { env } from '../env'
import { AuthErrors } from '../errors'
import { smsService } from './sms-service'
import type { CodeType } from '../types'

/**
 * 验证码管理器
 * 负责验证码的生成、存储、验证和清理
 */
export class CodeManager {
  /**
   * 生成验证码
   */
  private generateCode(): string {
    const length = parseInt(env.SMS_CODE_LENGTH)
    const digits = '0123456789'
    let code = ''
    
    for (let i = 0; i < length; i++) {
      code += digits[Math.floor(Math.random() * digits.length)]
    }
    
    return code
  }

  /**
   * 发送验证码
   */
  async sendCode(phone: string, type: CodeType): Promise<void> {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      throw AuthErrors.InvalidCredentials()
    }

    // 检查发送频率限制
    await this.checkRateLimit(phone)

    // 生成验证码
    const code = this.generateCode()
    const expiresAt = new Date(Date.now() + parseInt(env.SMS_CODE_EXPIRE_TIME) * 1000)

    try {
      // 删除旧的验证码
      await db.verification.deleteMany({
        where: {
          identifier: phone,
        },
      })

      // 存储新验证码
      await db.verification.create({
        data: {
          identifier: phone,
          value: code,
          expiresAt,
        },
      })

      // 发送短信
      await smsService.sendCode(phone, code, type)
      
      console.log(`✅ 验证码已发送到 ${phone}`)
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw AuthErrors.InternalError('验证码发送失败')
    }
  }

  /**
   * 验证验证码
   */
  async verifyCode(phone: string, code: string): Promise<boolean> {
    try {
      // 查找验证码记录
      const verification = await db.verification.findFirst({
        where: {
          identifier: phone,
          value: code,
        },
      })

      if (!verification) {
        throw AuthErrors.InvalidCode()
      }

      // 检查是否过期
      if (verification.expiresAt < new Date()) {
        // 删除过期的验证码
        await db.verification.delete({
          where: { id: verification.id },
        })
        throw AuthErrors.CodeExpired()
      }

      // 验证成功，删除验证码
      await db.verification.delete({
        where: { id: verification.id },
      })

      return true
    } catch (error) {
      if (error instanceof Error && error.message.includes('AuthError')) {
        throw error
      }
      console.error('验证码验证失败:', error)
      throw AuthErrors.InternalError('验证码验证失败')
    }
  }

  /**
   * 检查发送频率限制
   */
  private async checkRateLimit(phone: string): Promise<void> {
    const rateLimitSeconds = parseInt(env.SMS_RATE_LIMIT)
    const cutoffTime = new Date(Date.now() - rateLimitSeconds * 1000)

    // 查找最近发送的验证码
    const recentCode = await db.verification.findFirst({
      where: {
        identifier: phone,
        createdAt: {
          gte: cutoffTime,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    if (recentCode) {
      throw AuthErrors.RateLimitExceeded()
    }
  }

  /**
   * 清理过期的验证码
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const result = await db.verification.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      })
      
      if (result.count > 0) {
        console.log(`🧹 清理了 ${result.count} 个过期验证码`)
      }
    } catch (error) {
      console.error('清理过期验证码失败:', error)
    }
  }
}

// 导出默认实例
export const codeManager = new CodeManager()

// 定期清理过期验证码（每5分钟执行一次）
if (env.NODE_ENV !== 'test') {
  setInterval(() => {
    codeManager.cleanupExpiredCodes()
  }, 5 * 60 * 1000)
}
