import { env } from '../env'
import { AuthErrors } from '../errors'

/**
 * 频率限制器
 * 用于防止短信验证码被恶意发送
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map()
  private readonly maxAttempts: number
  private readonly windowMs: number

  constructor(maxAttempts = 5, windowMs = 60 * 1000) {
    this.maxAttempts = maxAttempts
    this.windowMs = windowMs
  }

  /**
   * 检查是否超过频率限制
   */
  checkLimit(identifier: string): void {
    const now = Date.now()
    const attempts = this.attempts.get(identifier) || []
    
    // 清理过期的尝试记录
    const validAttempts = attempts.filter(time => now - time < this.windowMs)
    
    if (validAttempts.length >= this.maxAttempts) {
      throw AuthErrors.RateLimitExceeded()
    }
    
    // 记录本次尝试
    validAttempts.push(now)
    this.attempts.set(identifier, validAttempts)
  }

  /**
   * 重置指定标识符的限制
   */
  reset(identifier: string): void {
    this.attempts.delete(identifier)
  }

  /**
   * 清理过期的记录
   */
  cleanup(): void {
    const now = Date.now()
    
    for (const [identifier, attempts] of this.attempts.entries()) {
      const validAttempts = attempts.filter(time => now - time < this.windowMs)
      
      if (validAttempts.length === 0) {
        this.attempts.delete(identifier)
      } else {
        this.attempts.set(identifier, validAttempts)
      }
    }
  }
}

/**
 * IP 频率限制器
 */
export class IpRateLimiter extends RateLimiter {
  constructor() {
    // IP限制：每分钟最多10次请求
    super(10, 60 * 1000)
  }
}

/**
 * 手机号频率限制器
 */
export class PhoneRateLimiter extends RateLimiter {
  constructor() {
    // 手机号限制：每小时最多5次请求
    super(5, 60 * 60 * 1000)
  }
}

// 导出默认实例
export const ipRateLimiter = new IpRateLimiter()
export const phoneRateLimiter = new PhoneRateLimiter()

// 定期清理过期记录
if (env.NODE_ENV !== 'test') {
  setInterval(() => {
    ipRateLimiter.cleanup()
    phoneRateLimiter.cleanup()
  }, 5 * 60 * 1000) // 每5分钟清理一次
}
