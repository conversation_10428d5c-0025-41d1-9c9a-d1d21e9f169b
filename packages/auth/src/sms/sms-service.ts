import { env } from '../env'
import { AuthErrors } from '../errors'
import type { CodeType } from '../types'

/**
 * 短信服务接口
 */
export interface SmsService {
  sendCode(phone: string, code: string, type: CodeType): Promise<boolean>
}

/**
 * 控制台短信服务（开发环境使用）
 */
export class ConsoleSmsService implements SmsService {
  async sendCode(phone: string, code: string, type: CodeType): Promise<boolean> {
    console.log(`📱 [SMS] 发送验证码到 ${phone}`)
    console.log(`📱 [SMS] 验证码: ${code}`)
    console.log(`📱 [SMS] 类型: ${type}`)
    console.log(`📱 [SMS] 过期时间: ${env.SMS_CODE_EXPIRE_TIME}秒`)
    return true
  }
}

/**
 * 阿里云短信服务
 */
export class AliyunSmsService implements SmsService {
  private accessKeyId: string
  private accessKeySecret: string
  private signName: string
  private templateCode: string

  constructor() {
    if (!env.SMS_ACCESS_KEY_ID || !env.SMS_ACCESS_KEY_SECRET) {
      throw new Error('阿里云短信服务配置不完整')
    }
    
    this.accessKeyId = env.SMS_ACCESS_KEY_ID
    this.accessKeySecret = env.SMS_ACCESS_KEY_SECRET
    this.signName = env.SMS_SIGN_NAME || 'COOZF'
    this.templateCode = env.SMS_TEMPLATE_CODE || 'SMS_123456789'
  }

  async sendCode(phone: string, code: string, type: CodeType): Promise<boolean> {
    try {
      // 这里实现阿里云短信发送逻辑
      // 需要安装 @alicloud/dysmsapi20170525 包
      
      console.log(`发送阿里云短信到 ${phone}, 验证码: ${code}`)
      
      // 模拟发送成功
      return true
    } catch (error) {
      console.error('阿里云短信发送失败:', error)
      throw AuthErrors.InternalError('短信发送失败')
    }
  }
}

/**
 * 短信服务工厂
 */
export class SmsServiceFactory {
  static create(): SmsService {
    // 根据环境和配置选择短信服务
    if (env.NODE_ENV === 'development' || !env.SMS_ACCESS_KEY_ID) {
      return new ConsoleSmsService()
    }
    
    return new AliyunSmsService()
  }
}

// 导出默认短信服务实例
export const smsService = SmsServiceFactory.create()
