import type { User, Session, Account, Verification } from '@coozf/db'

/**
 * 认证相关类型定义
 */

// 基础用户信息
export interface AuthUser extends Omit<User, 'password'> {
  id: string
  email: string | null
  phone: string | null
  name: string | null
  phoneVerified: boolean
  emailVerified: boolean
  avatar: string | null
  createdAt: Date
  updatedAt: Date
}

// 会话信息
export interface AuthSession extends Session {
  user: AuthUser
}

// 登录请求
export interface LoginRequest {
  phone?: string
  email?: string
  password?: string
  code?: string // 验证码
}

// 注册请求
export interface RegisterRequest {
  phone?: string
  email?: string
  password?: string
  name?: string
  code?: string // 验证码
}

// 发送验证码请求
export interface SendCodeRequest {
  phone: string
  type: 'login' | 'register' | 'reset-password'
}

// 验证码验证请求
export interface VerifyCodeRequest {
  phone: string
  code: string
  type: 'login' | 'register' | 'reset-password'
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 登录响应
export interface LoginResponse {
  user: AuthUser
  session: Session
  token: string
}

// 验证码类型
export type CodeType = 'login' | 'register' | 'reset-password' | 'verify-phone'

// 短信验证码配置
export interface SmsConfig {
  accessKeyId: string
  accessKeySecret: string
  signName: string
  templateCode: string
  expireTime: number // 过期时间（秒）
  rateLimit: number // 频率限制（秒）
}

// 验证码记录
export interface CodeRecord {
  phone: string
  code: string
  type: CodeType
  expiresAt: Date
  attempts: number
  createdAt: Date
}

// 认证错误类型
export enum AuthErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS',
  INVALID_CODE = 'INVALID_CODE',
  CODE_EXPIRED = 'CODE_EXPIRED',
  CODE_NOT_FOUND = 'CODE_NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  PHONE_NOT_VERIFIED = 'PHONE_NOT_VERIFIED',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

// 认证错误
export interface AuthError {
  code: AuthErrorCode
  message: string
  details?: any
}

// 中间件上下文
export interface AuthContext {
  user?: AuthUser
  session?: Session
  isAuthenticated: boolean
}

// 权限检查函数类型
export type PermissionChecker = (user: AuthUser, resource?: string) => boolean | Promise<boolean>

// 认证配置
export interface AuthConfig {
  baseUrl: string
  secret: string
  sessionExpiry: number
  sms?: SmsConfig
  redis?: {
    url: string
  }
}
