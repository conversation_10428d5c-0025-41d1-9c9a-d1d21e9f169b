{"name": "@coozf/auth", "version": "0.1.0", "private": true, "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./client": {"types": "./dist/client.d.ts", "default": "./src/client.ts"}, "./server": {"types": "./dist/server.d.ts", "default": "./src/server.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc --watch", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@coozf/db": "workspace:*", "better-auth": "^1.2.9", "zod": "catalog:", "bcryptjs": "^2.4.3", "nanoid": "^5.0.9"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "jest": "^29.7.0", "ts-jest": "^29.2.5"}, "prettier": "@acme/prettier-config"}