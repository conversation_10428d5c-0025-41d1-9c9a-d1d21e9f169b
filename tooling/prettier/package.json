{"name": "@coozf/prettier-config", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.2", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.12"}, "devDependencies": {"@coozf/tsconfig": "workspace:*", "@types/node": "^22.15.29", "typescript": "catalog:"}, "prettier": "@coozf/prettier-config"}