/// <reference types="./types.d.ts" />

import * as path from "node:path";
import { includeIgnoreFile } from "@eslint/compat";
import eslint from "@eslint/js";
import importPlugin from "eslint-plugin-import";
// import turboPlugin from "eslint-plugin-turbo";
import tseslint from "typescript-eslint";

/**
 * All packages that leverage t3-env should use this rule
 */
// export const restrictEnvAccess = tseslint.config(
//   { ignores: ["**/env.ts"] },
//   {
//     files: ["**/*.js", "**/*.ts", "**/*.tsx"],
//     rules: {
//       "no-restricted-properties": [
//         "error",
//         {
//           object: "process",
//           property: "env",
//           message:
//             "Use `import { env } from '~/env'` instead to ensure validated types.",
//         },
//       ],
//       "no-restricted-imports": [
//         "error",
//         {
//           name: "process",
//           importNames: ["env"],
//           message:
//             "Use `import { env } from '~/env'` instead to ensure validated types.",
//         },
//       ],
//     },
//   },
// );

export default tseslint.config(
  // Ignore files not tracked by VCS and any config files
  includeIgnoreFile(path.join(import.meta.dirname, '../.gitignore')),
  { ignores: ['**/*.config.*', '.env'] },
  {
    files: ['**/*.js', '**/*.ts', '**/*.tsx'],
    plugins: {
      import: importPlugin,
    },
    extends: [eslint.configs.recommended, ...tseslint.configs.recommended],
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      '@typescript-eslint/consistent-type-imports': [
        'warn',
        { prefer: 'type-imports', fixStyle: 'separate-type-imports' },
      ],
      '@typescript-eslint/no-misused-promises': [2, { checksVoidReturn: { attributes: false } }],
      '@typescript-eslint/no-unnecessary-condition': [
        'error',
        {
          allowConstantLoopConditions: true,
        },
      ],
      'import/consistent-type-specifier-style': ['error', 'prefer-top-level'],
    },
  },
  {
    linterOptions: { reportUnusedDisableDirectives: true },
    languageOptions: { parserOptions: { projectService: true } },
  }
)

