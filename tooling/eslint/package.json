{"name": "@coozf/eslint-config", "private": true, "version": "0.3.0", "type": "module", "exports": {"./base": "./base.js", "./nextjs": "./nextjs.js", "./react": "./react.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@eslint/compat": "^1.2.9", "@next/eslint-plugin-next": "^15.3.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "eslint-plugin-turbo": "^2.5.4", "typescript-eslint": "^8.33.1"}, "devDependencies": {"@coozf/prettier-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@coozf/prettier-config"}